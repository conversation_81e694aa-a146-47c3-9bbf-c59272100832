const express = require('express');
const router = express.Router();
const { verifyToken } = require('../services/authService');
const dbClient = require('../services/dbClient');
const { checkAdminPermission } = require('../utils/helpers');
const { validatePaginationParams, isValidCardCode } = require('../utils/validators');
const { getAllPackages, getPackageConfig } = require('../utils/config');

// 生成符合验证器要求的32位卡密
function generateValidCardCode() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 获取套餐描述
function getPackageDescription(packageType, packageConfig) {
  const descriptions = {
    'M': '标准月套餐',
    'Q': '标准季度套餐',
    'H': '标准半年套餐',
    'PM': 'PRO月套餐',
    'PQ': 'PRO季度套餐',
    'PH': 'PRO半年套餐',
    'PT': '测试套餐'
  };

  return descriptions[packageType] || `${packageType}套餐`;
}

// 管理员中间件
async function adminMiddleware(req, res, next) {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ error: 'Token required' });
    }

    const username = await verifyToken(token);
    await checkAdminPermission(username);
    
    req.adminUser = username;
    next();
  } catch (error) {
    console.error('Admin middleware error:', error);
    if (error.message.includes('Token') || error.message.includes('Invalid')) {
      res.status(401).json({ error: 'Authentication failed' });
    } else if (error.message.includes('管理员')) {
      res.status(403).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}

// 获取所有用户列表
router.get('/users', adminMiddleware, async (req, res) => {
  try {
    const validation = validatePaginationParams(req.query);
    if (!validation.isValid) {
      return res.status(400).json({ errors: validation.errors });
    }

    const { limit, offset } = validation.params;
    const { search } = req.query;

    let query = 'SELECT username, email, created_at, updated_at, vip_info, usage_stats FROM users';
    let countQuery = 'SELECT COUNT(*) FROM users';
    const queryParams = [];
    let paramIndex = 1;

    if (search) {
      const searchCondition = ` WHERE username ILIKE $${paramIndex} OR email ILIKE $${paramIndex}`;
      query += searchCondition;
      countQuery += searchCondition;
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    query += ` ORDER BY created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    queryParams.push(limit, offset);

    const [usersResult, countResult] = await Promise.all([
      dbClient.query(query, queryParams),
      dbClient.query(countQuery, search ? [`%${search}%`] : [])
    ]);

    const users = usersResult.rows.map(user => {
      const vip = user.vip_info || {};
      const usage = user.usage_stats || {};
      
      return {
        username: user.username,
        email: user.email,
        createdAt: user.created_at,
        updatedAt: user.updated_at,
        vip: {
          type: vip.type || null,
          expireAt: vip.expireAt || 0,
          quotaChars: vip.quotaChars || 0,
          usedChars: vip.usedChars || 0,
          isExpired: vip.expireAt ? Date.now() > vip.expireAt : true
        },
        usage: {
          totalChars: usage.totalChars || 0,
          monthlyChars: usage.monthlyChars || 0
        }
      };
    });

    const total = parseInt(countResult.rows[0].count);

    res.json({
      users: users,
      pagination: {
        total: total,
        limit: limit,
        offset: offset,
        hasMore: offset + limit < total
      }
    });
  } catch (error) {
    console.error('Admin get users error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 获取系统统计信息
router.get('/stats', adminMiddleware, async (req, res) => {
  try {
    // 用户统计
    const userStatsResult = await dbClient.query(`
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN vip_info->>'expireAt' IS NOT NULL AND (vip_info->>'expireAt')::bigint > EXTRACT(EPOCH FROM NOW()) * 1000 THEN 1 END) as active_vip_users,
        COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END) as new_users_7d,
        COUNT(CASE WHEN created_at >= NOW() - INTERVAL '30 days' THEN 1 END) as new_users_30d
      FROM users
    `);

    // 任务统计
    const taskStatsResult = await dbClient.query(`
      SELECT 
        COUNT(*) as total_tasks,
        COUNT(CASE WHEN status = 'complete' THEN 1 END) as completed_tasks,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_tasks,
        COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_tasks,
        COUNT(CASE WHEN created_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as tasks_24h,
        COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END) as tasks_7d
      FROM task_status
    `);

    // 卡密统计
    const cardStatsResult = await dbClient.query(`
      SELECT 
        COUNT(*) as total_cards,
        COUNT(CASE WHEN status = 'unused' THEN 1 END) as unused_cards,
        COUNT(CASE WHEN status = 'used' THEN 1 END) as used_cards,
        COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END) as new_cards_7d
      FROM cards
    `);

    // 最近7天的任务趋势
    const taskTrendResult = await dbClient.query(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as tasks,
        COUNT(CASE WHEN status = 'complete' THEN 1 END) as completed,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed
      FROM task_status 
      WHERE created_at >= NOW() - INTERVAL '7 days'
      GROUP BY DATE(created_at)
      ORDER BY date
    `);

    res.json({
      users: userStatsResult.rows[0],
      tasks: taskStatsResult.rows[0],
      cards: cardStatsResult.rows[0],
      taskTrend: taskTrendResult.rows.map(row => ({
        date: row.date,
        tasks: parseInt(row.tasks),
        completed: parseInt(row.completed),
        failed: parseInt(row.failed)
      })),
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Admin get stats error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 获取用户详细信息
router.get('/users/:username', adminMiddleware, async (req, res) => {
  try {
    const { username } = req.params;

    const userResult = await dbClient.query(
      'SELECT * FROM users WHERE username = $1',
      [username]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const user = userResult.rows[0];
    const vip = user.vip_info || {};
    const usage = user.usage_stats || {};

    // 获取用户的任务历史
    const tasksResult = await dbClient.query(
      'SELECT task_id, status, created_at, completed_at FROM task_status WHERE username = $1 ORDER BY created_at DESC LIMIT 10',
      [username]
    );

    // 获取用户使用的卡密
    const cardsResult = await dbClient.query(
      'SELECT code, package_type, used_at FROM cards WHERE used_by = $1 ORDER BY used_at DESC LIMIT 10',
      [username]
    );

    res.json({
      user: {
        username: user.username,
        email: user.email,
        passwordHash: user.password_hash,
        createdAt: user.created_at,
        updatedAt: user.updated_at,
        vip: vip,
        usage: usage
      },
      recentTasks: tasksResult.rows,
      usedCards: cardsResult.rows
    });
  } catch (error) {
    console.error('Admin get user detail error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 更新用户VIP信息
router.put('/users/:username/vip', adminMiddleware, async (req, res) => {
  try {
    const { username } = req.params;
    const { type, expireAt, quotaChars, usedChars } = req.body;

    // 验证参数
    if (type && !['M', 'Q', 'H', 'PM', 'PQ', 'PH', 'PT', 'T'].includes(type)) {
      return res.status(400).json({ error: 'Invalid VIP type' });
    }

    if (expireAt && (typeof expireAt !== 'number' || expireAt < 0)) {
      return res.status(400).json({ error: 'Invalid expireAt timestamp' });
    }

    if (quotaChars !== undefined && (typeof quotaChars !== 'number' || quotaChars < 0)) {
      return res.status(400).json({ error: 'Invalid quotaChars' });
    }

    if (usedChars !== undefined && (typeof usedChars !== 'number' || usedChars < 0)) {
      return res.status(400).json({ error: 'Invalid usedChars' });
    }

    // 获取当前用户信息
    const userResult = await dbClient.query(
      'SELECT vip_info FROM users WHERE username = $1',
      [username]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    const currentVip = userResult.rows[0].vip_info || {};

    // 更新VIP信息
    const updatedVip = {
      ...currentVip,
      ...(type !== undefined && { type }),
      ...(expireAt !== undefined && { expireAt }),
      ...(quotaChars !== undefined && { quotaChars }),
      ...(usedChars !== undefined && { usedChars })
    };

    await dbClient.query(
      'UPDATE users SET vip_info = $1, updated_at = CURRENT_TIMESTAMP WHERE username = $2',
      [JSON.stringify(updatedVip), username]
    );

    console.log(`[ADMIN] ${req.adminUser} updated VIP info for user ${username}`);

    res.json({
      success: true,
      message: 'VIP information updated successfully',
      vip: updatedVip
    });
  } catch (error) {
    console.error('Admin update user VIP error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 生成卡密
router.post('/cards/generate', adminMiddleware, async (req, res) => {
  try {
    const { packageType, quantity = 1, customCode } = req.body;

    // 验证套餐类型
    if (!packageType) {
      return res.status(400).json({ error: '套餐类型不能为空' });
    }

    const packageConfig = getPackageConfig(packageType);
    if (!packageConfig) {
      return res.status(400).json({
        error: '无效的套餐类型',
        availableTypes: Object.keys(getAllPackages())
      });
    }

    // 验证数量
    if (quantity < 1 || quantity > 100) {
      return res.status(400).json({ error: '生成数量必须在1-100之间' });
    }

    // 如果提供了自定义卡密，验证格式
    if (customCode && !isValidCardCode(customCode)) {
      return res.status(400).json({ error: '自定义卡密格式不正确（需要32位字母数字组合）' });
    }

    const generatedCards = [];
    const errors = [];

    // 生成卡密
    for (let i = 0; i < quantity; i++) {
      try {
        let cardCode;

        if (customCode && quantity === 1) {
          // 使用自定义卡密（仅当数量为1时）
          cardCode = customCode;
        } else {
          // 生成随机卡密
          cardCode = generateValidCardCode();

          // 确保卡密唯一性
          let attempts = 0;
          while (attempts < 10) {
            const existingCard = await dbClient.query(
              'SELECT id FROM cards WHERE code = $1',
              [cardCode]
            );

            if (existingCard.rows.length === 0) {
              break; // 卡密唯一，可以使用
            }

            cardCode = generateValidCardCode();
            attempts++;
          }

          if (attempts >= 10) {
            errors.push(`第${i + 1}张卡密生成失败：无法生成唯一卡密`);
            continue;
          }
        }

        // 构建package_info
        const packageInfo = {
          type: packageType,
          duration: packageConfig.days * 86400000, // 转换为毫秒
          quotaChars: packageConfig.chars,
          price: packageConfig.price,
          description: getPackageDescription(packageType, packageConfig)
        };

        // 插入数据库
        await dbClient.query(`
          INSERT INTO cards (code, package_type, status, package_info, created_at)
          VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
        `, [
          cardCode,
          packageType,
          'unused',
          JSON.stringify(packageInfo)
        ]);

        generatedCards.push({
          code: cardCode,
          packageType: packageType,
          packageInfo: packageInfo
        });

      } catch (error) {
        console.error(`生成第${i + 1}张卡密失败:`, error);
        if (error.code === '23505') { // PostgreSQL唯一约束违反
          errors.push(`第${i + 1}张卡密生成失败：卡密已存在`);
        } else {
          errors.push(`第${i + 1}张卡密生成失败：${error.message}`);
        }
      }
    }

    // 返回结果
    const response = {
      success: generatedCards.length > 0,
      generated: generatedCards.length,
      requested: quantity,
      cards: generatedCards
    };

    if (errors.length > 0) {
      response.errors = errors;
    }

    if (generatedCards.length === 0) {
      return res.status(400).json({
        ...response,
        error: '没有成功生成任何卡密'
      });
    }

    res.status(201).json(response);
  } catch (error) {
    console.error('Generate cards error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 获取卡密列表
router.get('/cards', adminMiddleware, async (req, res) => {
  try {
    const { status, packageType } = req.query;

    // 验证分页参数
    const validation = validatePaginationParams(req.query);
    if (!validation.isValid) {
      return res.status(400).json({ errors: validation.errors });
    }

    const { limit: validatedLimit, offset } = validation.params;

    // 构建查询条件
    let whereConditions = [];
    let queryParams = [];
    let paramIndex = 1;

    if (status) {
      whereConditions.push(`c.status = $${paramIndex}`);
      queryParams.push(status);
      paramIndex++;
    }

    if (packageType) {
      whereConditions.push(`c.package_type = $${paramIndex}`);
      queryParams.push(packageType);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0
      ? `WHERE ${whereConditions.join(' AND ')}`
      : '';

    // 查询卡密列表（包含用户使用量信息）
    const cardsResult = await dbClient.query(`
      SELECT
        c.id, c.code, c.package_type, c.status, c.package_info,
        c.created_at, c.used_at, c.used_by,
        u.usage_stats
      FROM cards c
      LEFT JOIN users u ON c.used_by = u.username
      ${whereClause}
      ORDER BY c.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `, [...queryParams, validatedLimit, offset]);

    // 查询总数
    const countResult = await dbClient.query(`
      SELECT COUNT(*) as total FROM cards c ${whereClause}
    `, queryParams);

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / validatedLimit);

    // 处理返回数据，添加用户使用量信息
    const cards = cardsResult.rows.map(card => ({
      id: card.id,
      code: card.code,
      package_type: card.package_type,
      status: card.status,
      package_info: card.package_info,
      created_at: card.created_at,
      used_at: card.used_at,
      used_by: card.used_by,
      // 如果卡密已使用且有用户数据，则包含使用量信息
      userUsage: card.used_by && card.usage_stats ? card.usage_stats : null
    }));

    const currentPage = Math.floor(offset / validatedLimit) + 1;

    res.json({
      cards: cards,
      pagination: {
        page: currentPage,
        limit: validatedLimit,
        total: total,
        totalPages: totalPages,
        hasNext: currentPage < totalPages,
        hasPrev: currentPage > 1
      }
    });
  } catch (error) {
    console.error('Get cards error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 获取可用套餐类型
router.get('/cards/packages', adminMiddleware, async (req, res) => {
  try {
    const packages = getAllPackages();
    const packageList = Object.entries(packages).map(([type, config]) => ({
      type: type,
      description: getPackageDescription(type, config),
      days: config.days,
      price: config.price,
      chars: config.chars
    }));

    res.json({
      packages: packageList
    });
  } catch (error) {
    console.error('Get packages error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
