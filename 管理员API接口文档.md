# 管理员API接口文档

## 🔐 认证说明

所有管理员接口都需要在请求头中包含有效的JWT Token：

```http
Authorization: Bearer <admin_token>
```

**管理员权限验证**：
- 用户必须在 `ADMIN_USERS` 环境变量中配置
- 环境变量格式：`ADMIN_USERS="admin1,admin2,admin3"`

---

## 📋 卡密管理接口

### 1. 生成卡密

**接口地址**：`POST /api/admin/cards/generate`

**请求参数**：
```json
{
  "packageType": "PT",        // 必填：套餐类型
  "quantity": 1,              // 可选：生成数量，默认1，范围1-100
  "customCode": "optional"    // 可选：自定义32位卡密
}
```

**套餐类型说明**：
| 类型 | 名称 | 时长 | 价格 | 字符配额 |
|------|------|------|------|----------|
| `M` | 标准月套餐 | 30天 | ¥25 | 80,000字符 |
| `Q` | 标准季度套餐 | 90天 | ¥55 | 250,000字符 |
| `H` | 标准半年套餐 | 180天 | ¥99 | 550,000字符 |
| `PM` | PRO月套餐 | 30天 | ¥45 | 250,000字符 |
| `PQ` | PRO季度套餐 | 90天 | ¥120 | 800,000字符 |
| `PH` | PRO半年套餐 | 180天 | ¥220 | 2,000,000字符 |
| `PT` | 测试套餐 | 30分钟 | ¥0 | 5,000字符 |

**成功响应**：
```json
{
  "success": true,
  "generated": 1,
  "requested": 1,
  "cards": [
    {
      "code": "5ltcGRXIDHpRsaFbKGUBScKNORTlxV9Q",
      "packageType": "PT",
      "packageInfo": {
        "type": "PT",
        "duration": 1800000,
        "quotaChars": 5000,
        "price": 0,
        "description": "测试套餐"
      }
    }
  ]
}
```

**错误响应**：
```json
{
  "success": false,
  "generated": 0,
  "requested": 1,
  "error": "没有成功生成任何卡密",
  "errors": ["第1张卡密生成失败：卡密已存在"]
}
```

### 2. 获取卡密列表

**接口地址**：`GET /api/admin/cards`

**查询参数**：
```
?page=1&limit=20&status=unused&packageType=M
```

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `page` | number | 否 | 页码，默认1 |
| `limit` | number | 否 | 每页数量，默认20，最大100 |
| `status` | string | 否 | 卡密状态：`unused`、`used` |
| `packageType` | string | 否 | 套餐类型筛选 |

**成功响应**：
```json
{
  "cards": [
    {
      "id": 1,
      "code": "5ltcGRXIDHpRsaFbKGUBScKNORTlxV9Q",
      "package_type": "PT",
      "status": "unused",
      "package_info": {
        "type": "PT",
        "duration": 1800000,
        "quotaChars": 5000,
        "price": 0,
        "description": "测试套餐"
      },
      "created_at": "2024-01-15T10:30:00.000Z",
      "used_at": null,
      "used_by": null
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### 3. 获取可用套餐类型

**接口地址**：`GET /api/admin/cards/packages`

**成功响应**：
```json
{
  "packages": [
    {
      "type": "M",
      "description": "标准月套餐",
      "days": 30,
      "price": 25,
      "chars": 80000
    },
    {
      "type": "Q",
      "description": "标准季度套餐",
      "days": 90,
      "price": 55,
      "chars": 250000
    }
  ]
}
```

---

## 👥 用户管理接口

### 4. 获取用户列表

**接口地址**：`GET /api/admin/users`

**查询参数**：
```
?page=1&limit=20&search=username
```

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `page` | number | 否 | 页码，默认1 |
| `limit` | number | 否 | 每页数量，默认20，最大100 |
| `search` | string | 否 | 搜索用户名或邮箱 |

**成功响应**：
```json
{
  "users": [
    {
      "username": "testuser",
      "email": "<EMAIL>",
      "created_at": "2024-01-15T10:30:00.000Z",
      "updated_at": "2024-01-15T10:30:00.000Z",
      "vip_info": {
        "type": "M",
        "expireAt": 1705392600000,
        "quotaChars": 80000
      },
      "usage_stats": {
        "totalChars": 15000,
        "monthlyChars": 5000,
        "monthlyResetAt": 1704067200000
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### 5. 获取用户详细信息

**接口地址**：`GET /api/admin/users/:username`

**路径参数**：
- `username`：用户名

**成功响应**：
```json
{
  "user": {
    "username": "testuser",
    "email": "<EMAIL>",
    "passwordHash": "hashed_password",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z",
    "vip": {
      "type": "M",
      "expireAt": 1705392600000,
      "quotaChars": 80000
    },
    "usage": {
      "totalChars": 15000,
      "monthlyChars": 5000,
      "monthlyResetAt": 1704067200000
    }
  },
  "recentTasks": [
    {
      "task_id": "uuid-task-id",
      "status": "complete",
      "created_at": "2024-01-15T10:30:00.000Z",
      "completed_at": "2024-01-15T10:31:00.000Z"
    }
  ],
  "usedCards": [
    {
      "code": "5ltcGRXIDHpRsaFbKGUBScKNORTlxV9Q",
      "package_type": "M",
      "used_at": "2024-01-15T10:30:00.000Z"
    }
  ]
}
```

### 6. 更新用户VIP

**接口地址**：`PUT /api/admin/users/:username/vip`

**路径参数**：
- `username`：用户名

**请求参数**：
```json
{
  "type": "M",                    // VIP类型
  "expireAt": 1705392600000,      // 过期时间戳（毫秒）
  "quotaChars": 80000             // 字符配额
}
```

**成功响应**：
```json
{
  "message": "User VIP updated successfully",
  "vip": {
    "type": "M",
    "expireAt": 1705392600000,
    "quotaChars": 80000
  }
}
```

---

## 📊 系统统计接口

### 7. 获取系统统计

**接口地址**：`GET /api/admin/stats`

**成功响应**：
```json
{
  "userStats": {
    "total_users": "150",
    "active_vip_users": "45",
    "new_users_7d": "12",
    "new_users_30d": "38"
  },
  "taskStats": {
    "total_tasks": "1250",
    "completed_tasks": "1180",
    "failed_tasks": "70",
    "tasks_7d": "85"
  },
  "cardStats": {
    "total_cards": "200",
    "unused_cards": "150",
    "used_cards": "50",
    "new_cards_7d": "25"
  },
  "taskTrend": [
    {
      "date": "2024-01-15T00:00:00.000Z",
      "tasks": "15",
      "completed": "14",
      "failed": "1"
    }
  ]
}
```

---

## 🚨 错误响应格式

### 认证错误
```json
{
  "error": "Token required"
}
```

### 权限错误
```json
{
  "error": "需要管理员权限"
}
```

### 参数错误
```json
{
  "error": "套餐类型不能为空",
  "availableTypes": ["M", "Q", "H", "PM", "PQ", "PH", "PT"]
}
```

### 服务器错误
```json
{
  "error": "Internal server error"
}
```

---

## 📝 使用示例

### JavaScript/TypeScript 示例

```typescript
// 生成卡密
const generateCards = async (packageType: string, quantity: number) => {
  const response = await fetch('/api/admin/cards/generate', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      packageType,
      quantity
    })
  });
  
  return await response.json();
};

// 获取卡密列表
const getCards = async (page = 1, status?: string) => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: '20'
  });
  
  if (status) params.append('status', status);
  
  const response = await fetch(`/api/admin/cards?${params}`, {
    headers: {
      'Authorization': `Bearer ${adminToken}`
    }
  });
  
  return await response.json();
};
```

### cURL 示例

```bash
# 生成测试卡密
curl -X POST http://localhost:3001/api/admin/cards/generate \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"packageType": "PT", "quantity": 5}'

# 获取卡密列表
curl -X GET "http://localhost:3001/api/admin/cards?page=1&limit=20&status=unused" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# 获取系统统计
curl -X GET http://localhost:3001/api/admin/stats \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```
